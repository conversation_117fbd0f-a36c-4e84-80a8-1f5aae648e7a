"""
Game Interface Module for NFS: Most Wanted AI Agent
Handles screen capture, window detection, and input control
"""
import time
import numpy as np
import cv2
import mss
import pyautogui
import pygetwindow as gw
import psutil
from pynput import keyboard, mouse
from pynput.keyboard import Key
from typing import Optional, Tuple, Dict, Any
import logging

from config.settings import *

logger = logging.getLogger(__name__)

class GameInterface:
    """Handles all interactions with the game window"""
    
    def __init__(self):
        self.game_window = None
        self.screen_capture = mss.mss()
        self.is_capturing = False
        self.last_frame = None
        self.keyboard_controller = keyboard.Controller()
        self.mouse_controller = mouse.Controller()
        
        # Disable pyautogui failsafe
        pyautogui.FAILSAFE = False
        pyautogui.PAUSE = 0.01
        
    def find_game_window(self) -> bool:
        """Find and focus the game window"""
        try:
            windows = gw.getWindowsWithTitle(GAME_WINDOW_TITLE)
            if windows:
                self.game_window = windows[0]
                logger.info(f"Found game window: {self.game_window.title}")
                return True
            else:
                # Try alternative window titles
                alt_titles = ["Need for Speed", "NFS", "Most Wanted"]
                for title in alt_titles:
                    windows = gw.getWindowsWithTitle(title)
                    if windows:
                        self.game_window = windows[0]
                        logger.info(f"Found game window with alternative title: {self.game_window.title}")
                        return True
                        
                logger.warning("Game window not found")
                return False
        except Exception as e:
            logger.error(f"Error finding game window: {e}")
            return False
    
    def is_game_running(self) -> bool:
        """Check if the game process is running"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] == GAME_PROCESS_NAME:
                    return True
            return False
        except Exception as e:
            logger.error(f"Error checking game process: {e}")
            return False
    
    def focus_game_window(self) -> bool:
        """Bring game window to focus"""
        try:
            if self.game_window:
                self.game_window.activate()
                time.sleep(0.5)  # Wait for window to focus
                return True
            return False
        except Exception as e:
            logger.error(f"Error focusing game window: {e}")
            return False
    
    def get_game_region(self) -> Optional[Dict[str, int]]:
        """Get the game window region for screen capture"""
        try:
            if self.game_window:
                return {
                    'top': self.game_window.top,
                    'left': self.game_window.left,
                    'width': self.game_window.width,
                    'height': self.game_window.height
                }
            return None
        except Exception as e:
            logger.error(f"Error getting game region: {e}")
            return None
    
    def capture_screen(self, region: Optional[Dict[str, int]] = None) -> Optional[np.ndarray]:
        """Capture game screen"""
        try:
            if region is None:
                region = self.get_game_region()
            
            if region is None:
                # Fallback to full screen
                region = {'top': 0, 'left': 0, 'width': 1920, 'height': 1080}
            
            screenshot = self.screen_capture.grab(region)
            frame = np.array(screenshot)
            
            # Convert BGRA to RGB
            if frame.shape[2] == 4:
                frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2RGB)
            
            self.last_frame = frame
            return frame
            
        except Exception as e:
            logger.error(f"Error capturing screen: {e}")
            return None
    
    def preprocess_frame(self, frame: np.ndarray, target_size: Tuple[int, int] = (84, 84)) -> np.ndarray:
        """Preprocess frame for RL model"""
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(frame, cv2.COLOR_RGB2GRAY)
            
            # Resize
            resized = cv2.resize(gray, target_size)
            
            # Normalize
            normalized = resized.astype(np.float32) / 255.0
            
            return normalized
            
        except Exception as e:
            logger.error(f"Error preprocessing frame: {e}")
            return np.zeros(target_size, dtype=np.float32)
    
    def send_key_press(self, key: str, duration: float = 0.1):
        """Send a key press to the game"""
        try:
            # First ensure game window is focused
            self.focus_game_window()
            time.sleep(0.01)

            # Use pyautogui for more reliable input
            pyautogui.keyDown(key)
            time.sleep(duration)
            pyautogui.keyUp(key)

            logger.debug(f"Sent key press: {key}")

        except Exception as e:
            logger.error(f"Error sending key press {key}: {e}")
    
    def send_key_down(self, key: str):
        """Send key down event"""
        try:
            # Ensure game window is focused
            self.focus_game_window()
            time.sleep(0.01)

            pyautogui.keyDown(key)
            logger.debug(f"Key down: {key}")

        except Exception as e:
            logger.error(f"Error sending key down {key}: {e}")

    def send_key_up(self, key: str):
        """Send key up event"""
        try:
            pyautogui.keyUp(key)
            logger.debug(f"Key up: {key}")

        except Exception as e:
            logger.error(f"Error sending key up {key}: {e}")
    
    def execute_action(self, action: int, duration: float = 0.1):
        """Execute game action based on action index"""
        # Action mapping: 0=no_action, 1=left, 2=right, 3=accelerate, 4=brake, 5=handbrake, 6=boost, 7=left+accel, 8=right+accel

        try:
            # Ensure game window is focused first
            self.focus_game_window()
            time.sleep(0.01)

            # Release all keys first (using arrow keys for NFS)
            pyautogui.keyUp('left')     # left arrow
            pyautogui.keyUp('right')    # right arrow
            pyautogui.keyUp('up')       # up arrow (accelerate)
            pyautogui.keyUp('down')     # down arrow (brake)
            pyautogui.keyUp('space')    # handbrake
            pyautogui.keyUp('shift')    # boost

            # Execute the action
            if action == 0:  # No action
                pass
            elif action == 1:  # Left
                pyautogui.keyDown('left')
                logger.info("Action: Turn LEFT (←)")
            elif action == 2:  # Right
                pyautogui.keyDown('right')
                logger.info("Action: Turn RIGHT (→)")
            elif action == 3:  # Accelerate
                pyautogui.keyDown('up')
                logger.info("Action: ACCELERATE (↑)")
            elif action == 4:  # Brake
                pyautogui.keyDown('down')
                logger.info("Action: BRAKE (↓)")
            elif action == 5:  # Handbrake
                pyautogui.keyDown('space')
                logger.info("Action: HANDBRAKE (Space)")
            elif action == 6:  # Boost
                pyautogui.keyDown('shift')
                logger.info("Action: BOOST (Shift)")
            elif action == 7:  # Left + Accelerate
                pyautogui.keyDown('left')
                pyautogui.keyDown('up')
                logger.info("Action: LEFT + ACCELERATE (← + ↑)")
            elif action == 8:  # Right + Accelerate
                pyautogui.keyDown('right')
                pyautogui.keyDown('up')
                logger.info("Action: RIGHT + ACCELERATE (→ + ↑)")

        except Exception as e:
            logger.error(f"Error executing action {action}: {e}")
    
    def click_at_position(self, x: int, y: int, button: str = 'left'):
        """Click at specific screen position"""
        try:
            if self.game_window:
                # Convert relative position to absolute
                abs_x = self.game_window.left + x
                abs_y = self.game_window.top + y
                
                if button == 'left':
                    pyautogui.click(abs_x, abs_y)
                elif button == 'right':
                    pyautogui.rightClick(abs_x, abs_y)
                    
        except Exception as e:
            logger.error(f"Error clicking at position ({x}, {y}): {e}")
    
    def save_screenshot(self, filename: str = None) -> str:
        """Save current frame as screenshot"""
        try:
            if self.last_frame is not None:
                if filename is None:
                    timestamp = int(time.time())
                    filename = f"screenshot_{timestamp}.png"
                
                filepath = SCREENSHOTS_DIR / filename
                cv2.imwrite(str(filepath), cv2.cvtColor(self.last_frame, cv2.COLOR_RGB2BGR))
                logger.info(f"Screenshot saved: {filepath}")
                return str(filepath)
            else:
                logger.warning("No frame available for screenshot")
                return ""
                
        except Exception as e:
            logger.error(f"Error saving screenshot: {e}")
            return ""
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            # Release all keys
            for key in KEYBOARD_CONTROLS.values():
                self.send_key_up(key)
            
            if hasattr(self, 'screen_capture'):
                self.screen_capture.close()
                
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
