"""
Computer Vision Module for NFS: Most Wanted AI Agent
Handles UI detection, car selection, menu navigation using OpenCV and YOLO
"""
import cv2
import numpy as np
import torch
from ultralytics import YOL<PERSON>
from typing import List, Tuple, Dict, Optional
import logging
import os
from pathlib import Path

from config.settings import *

logger = logging.getLogger(__name__)

class ComputerVision:
    """Handles computer vision tasks for game UI detection"""
    
    def __init__(self):
        self.yolo_model = None
        self.template_cache = {}
        self.load_models()
        self.load_templates()
    
    def load_models(self):
        """Load YOLO model for UI detection"""
        try:
            if YOLO_MODEL_PATH.exists():
                self.yolo_model = YOLO(str(YOLO_MODEL_PATH))
                logger.info("YOLO model loaded successfully")
            else:
                # Use pre-trained YOLOv8 model as fallback
                self.yolo_model = YOLO('yolov8n.pt')
                logger.info("Using pre-trained YOLOv8 model")
        except Exception as e:
            logger.error(f"Error loading YOLO model: {e}")
            self.yolo_model = None
    
    def load_templates(self):
        """Load UI template images for template matching"""
        try:
            template_files = {
                'start_race': 'start_race_button.png',
                'car_selection': 'car_selection_screen.png',
                'map_screen': 'map_screen.png',
                'pause_menu': 'pause_menu.png',
                'race_finished': 'race_finished.png',
                'crash_screen': 'crash_screen.png',
                'loading_screen': 'loading_screen.png'
            }
            
            for name, filename in template_files.items():
                template_path = TEMPLATES_DIR / filename
                if template_path.exists():
                    template = cv2.imread(str(template_path), cv2.IMREAD_COLOR)
                    self.template_cache[name] = template
                    logger.info(f"Loaded template: {name}")
                else:
                    logger.warning(f"Template not found: {template_path}")
                    
        except Exception as e:
            logger.error(f"Error loading templates: {e}")
    
    def detect_ui_elements(self, frame: np.ndarray) -> List[Dict]:
        """Detect UI elements using YOLO"""
        try:
            if self.yolo_model is None:
                return []
            
            results = self.yolo_model(frame, conf=UI_DETECTION_CONFIDENCE)
            detections = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        
                        detection = {
                            'bbox': (int(x1), int(y1), int(x2), int(y2)),
                            'confidence': float(confidence),
                            'class_id': class_id,
                            'center': (int((x1 + x2) / 2), int((y1 + y2) / 2))
                        }
                        detections.append(detection)
            
            return detections
            
        except Exception as e:
            logger.error(f"Error detecting UI elements: {e}")
            return []
    
    def template_match(self, frame: np.ndarray, template_name: str) -> Optional[Tuple[int, int, float]]:
        """Find template in frame using template matching"""
        try:
            if template_name not in self.template_cache:
                logger.warning(f"Template not found: {template_name}")
                return None
            
            template = self.template_cache[template_name]
            
            # Convert to grayscale for matching
            frame_gray = cv2.cvtColor(frame, cv2.COLOR_RGB2GRAY)
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            
            # Perform template matching
            result = cv2.matchTemplate(frame_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= TEMPLATE_MATCH_THRESHOLD:
                # Calculate center position
                h, w = template_gray.shape
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                
                return (center_x, center_y, max_val)
            
            return None
            
        except Exception as e:
            logger.error(f"Error in template matching for {template_name}: {e}")
            return None
    
    def detect_game_state(self, frame: np.ndarray) -> str:
        """Detect current game state based on UI elements"""
        try:
            # Check for different game states using template matching
            states_to_check = [
                'loading_screen',
                'car_selection',
                'map_screen',
                'pause_menu',
                'race_finished',
                'crash_screen'
            ]
            
            for state in states_to_check:
                match = self.template_match(frame, state)
                if match:
                    logger.info(f"Detected game state: {state}")
                    return state
            
            # If no specific state detected, assume in-race
            return 'in_race'
            
        except Exception as e:
            logger.error(f"Error detecting game state: {e}")
            return 'unknown'
    
    def find_clickable_elements(self, frame: np.ndarray) -> List[Dict]:
        """Find clickable UI elements like buttons"""
        try:
            clickable_elements = []
            
            # Use template matching for known buttons
            button_templates = ['start_race']
            
            for template_name in button_templates:
                match = self.template_match(frame, template_name)
                if match:
                    x, y, confidence = match
                    clickable_elements.append({
                        'type': template_name,
                        'position': (x, y),
                        'confidence': confidence
                    })
            
            # Use YOLO detections for additional elements
            detections = self.detect_ui_elements(frame)
            for detection in detections:
                clickable_elements.append({
                    'type': f"yolo_class_{detection['class_id']}",
                    'position': detection['center'],
                    'confidence': detection['confidence'],
                    'bbox': detection['bbox']
                })
            
            return clickable_elements
            
        except Exception as e:
            logger.error(f"Error finding clickable elements: {e}")
            return []
    
    def detect_cars_in_selection(self, frame: np.ndarray) -> List[Dict]:
        """Detect available cars in car selection screen"""
        try:
            cars = []
            
            # Use contour detection to find car thumbnails
            gray = cv2.cvtColor(frame, cv2.COLOR_RGB2GRAY)
            
            # Apply threshold to find car selection boxes
            _, thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            
            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if 1000 < area < 50000:  # Filter by area to find car thumbnails
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Check aspect ratio (cars are typically wider than tall)
                    aspect_ratio = w / h
                    if 1.2 < aspect_ratio < 3.0:
                        cars.append({
                            'bbox': (x, y, x + w, y + h),
                            'center': (x + w // 2, y + h // 2),
                            'area': area
                        })
            
            # Sort by position (left to right, top to bottom)
            cars.sort(key=lambda car: (car['center'][1], car['center'][0]))
            
            return cars
            
        except Exception as e:
            logger.error(f"Error detecting cars in selection: {e}")
            return []
    
    def detect_minimap_elements(self, frame: np.ndarray) -> Dict:
        """Detect elements on the minimap"""
        try:
            # Assume minimap is in top-right corner
            h, w = frame.shape[:2]
            minimap_region = frame[0:h//4, 3*w//4:w]
            
            # Convert to HSV for better color detection
            hsv = cv2.cvtColor(minimap_region, cv2.COLOR_RGB2HSV)
            
            # Detect player position (usually a different color)
            # This is game-specific and would need tuning
            player_mask = cv2.inRange(hsv, (0, 100, 100), (10, 255, 255))  # Red range
            
            # Find player position
            contours, _ = cv2.findContours(player_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            player_pos = None
            if contours:
                largest_contour = max(contours, key=cv2.contourArea)
                M = cv2.moments(largest_contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    player_pos = (cx + 3*w//4, cy)  # Convert back to full frame coordinates
            
            return {
                'player_position': player_pos,
                'minimap_region': (3*w//4, 0, w, h//4)
            }
            
        except Exception as e:
            logger.error(f"Error detecting minimap elements: {e}")
            return {}
    
    def extract_speed_info(self, frame: np.ndarray) -> Optional[float]:
        """Extract speed information from speedometer"""
        try:
            # Speedometer is usually in bottom-right corner
            h, w = frame.shape[:2]
            speedometer_region = frame[3*h//4:h, 3*w//4:w]
            
            # Convert to grayscale and apply OCR-friendly preprocessing
            gray = cv2.cvtColor(speedometer_region, cv2.COLOR_RGB2GRAY)
            
            # Apply threshold to make text more visible
            _, thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            
            # This would require OCR (pytesseract) to extract actual numbers
            # For now, return a placeholder
            return None
            
        except Exception as e:
            logger.error(f"Error extracting speed info: {e}")
            return None
    
    def visualize_detections(self, frame: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """Draw detection results on frame for debugging"""
        try:
            vis_frame = frame.copy()
            
            for detection in detections:
                if 'bbox' in detection:
                    x1, y1, x2, y2 = detection['bbox']
                    cv2.rectangle(vis_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    
                    # Add label
                    label = f"{detection.get('type', 'unknown')}: {detection.get('confidence', 0):.2f}"
                    cv2.putText(vis_frame, label, (x1, y1 - 10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                
                elif 'position' in detection:
                    x, y = detection['position']
                    cv2.circle(vis_frame, (x, y), 5, (255, 0, 0), -1)
                    
                    label = f"{detection.get('type', 'unknown')}"
                    cv2.putText(vis_frame, label, (x + 10, y), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
            
            return vis_frame
            
        except Exception as e:
            logger.error(f"Error visualizing detections: {e}")
            return frame
