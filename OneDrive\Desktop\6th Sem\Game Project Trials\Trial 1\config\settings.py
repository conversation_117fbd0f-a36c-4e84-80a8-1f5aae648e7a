"""
Configuration settings for NFS: Most Wanted AI Agent
"""
import os
from pathlib import Path

# Base paths
BASE_DIR = Path(__file__).parent.parent
MODELS_DIR = BASE_DIR / "models"
TEMPLATES_DIR = BASE_DIR / "templates"
LOGS_DIR = BASE_DIR / "logs"
SCREENSHOTS_DIR = BASE_DIR / "screenshots"

# Create directories if they don't exist
for dir_path in [MODELS_DIR, TEMPLATES_DIR, LOGS_DIR, SCREENSHOTS_DIR]:
    dir_path.mkdir(exist_ok=True)

# Game settings
GAME_WINDOW_TITLE = "Need for Speed™ Most Wanted"
GAME_PROCESS_NAME = "NFS13.exe"
TARGET_FPS = 30
SCREEN_CAPTURE_REGION = None  # None for full screen, or (x, y, width, height)

# Computer Vision settings
TEMPLATE_MATCH_THRESHOLD = 0.8
UI_DETECTION_CONFIDENCE = 0.7
YOLO_MODEL_PATH = MODELS_DIR / "yolo_ui_detection.pt"

# Reinforcement Learning settings
RL_MODEL_PATH = MODELS_DIR / "nfs_dqn_agent.zip"
STATE_SHAPE = (84, 84, 4)  # Grayscale frames stacked
ACTION_SPACE_SIZE = 9  # [no_action, left, right, up, down, brake, boost, left+up, right+up]
LEARNING_RATE = 0.0001
BATCH_SIZE = 32
MEMORY_SIZE = 100000
EXPLORATION_RATE = 1.0
EXPLORATION_DECAY = 0.995
EXPLORATION_MIN = 0.01
TRAINING_FREQUENCY = 4
TARGET_UPDATE_FREQUENCY = 1000

# Input controls mapping (NFS: Most Wanted uses arrow keys)
KEYBOARD_CONTROLS = {
    'accelerate': 'up',
    'brake': 'down',
    'left': 'left',
    'right': 'right',
    'boost': 'shift',
    'handbrake': 'space',
    'reset': 'r',
    'pause': 'esc'
}

# PS5 Controller mapping (using pygame)
PS5_CONTROLS = {
    'accelerate': 'R2',
    'brake': 'L2',
    'left': 'left_stick_x_neg',
    'right': 'left_stick_x_pos',
    'boost': 'X',
    'handbrake': 'square',
    'reset': 'triangle',
    'pause': 'options'
}

# Voice commands
VOICE_COMMANDS = {
    'start_ai': ['start ai', 'begin ai', 'ai mode on'],
    'stop_ai': ['stop ai', 'ai mode off', 'manual mode'],
    'pause_ai': ['pause ai', 'pause'],
    'resume_ai': ['resume ai', 'continue'],
    'take_screenshot': ['screenshot', 'capture screen'],
    'save_model': ['save model', 'save progress']
}

# Logging settings
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
ENABLE_TENSORBOARD = True
TENSORBOARD_LOG_DIR = LOGS_DIR / "tensorboard"

# Training settings
EPISODES_PER_TRAINING = 100
SAVE_MODEL_FREQUENCY = 50
SCREENSHOT_FREQUENCY = 10
MAX_EPISODE_LENGTH = 300  # seconds
REWARD_WEIGHTS = {
    'speed': 1.0,
    'position': 2.0,
    'crash_penalty': -10.0,
    'wrong_direction_penalty': -5.0,
    'completion_bonus': 100.0
}

# Performance settings
USE_GPU = False  # Set to False since CUDA not available
DEVICE = "cpu"
NUM_WORKERS = 4
ENABLE_MULTIPROCESSING = True

# Debug settings
DEBUG_MODE = False
SAVE_DEBUG_IMAGES = True
SHOW_CV_WINDOWS = False
VERBOSE_LOGGING = True
