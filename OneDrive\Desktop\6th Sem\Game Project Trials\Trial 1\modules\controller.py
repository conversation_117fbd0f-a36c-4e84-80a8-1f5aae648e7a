"""
Controller Interface Module for NFS: Most Wanted AI Agent
Handles PS5 controller input and manual override functionality
"""
import pygame
import threading
import time
import logging
from typing import Dict, Optional, Callable
from dataclasses import dataclass

from config.settings import *

logger = logging.getLogger(__name__)

@dataclass
class ControllerState:
    """Represents the current state of the controller"""
    left_stick_x: float = 0.0
    left_stick_y: float = 0.0
    right_stick_x: float = 0.0
    right_stick_y: float = 0.0
    left_trigger: float = 0.0
    right_trigger: float = 0.0
    buttons: Dict[str, bool] = None
    
    def __post_init__(self):
        if self.buttons is None:
            self.buttons = {
                'X': False, 'O': False, 'square': False, 'triangle': False,
                'L1': False, 'R1': False, 'L2': False, 'R2': False,
                'share': False, 'options': False, 'ps': False,
                'left_stick': False, 'right_stick': False,
                'dpad_up': False, 'dpad_down': False, 'dpad_left': False, 'dpad_right': False
            }

class PS5Controller:
    """PS5 Controller interface using pygame"""
    
    def __init__(self, on_input_callback: Optional[Callable] = None):
        self.controller = None
        self.is_connected = False
        self.is_running = False
        self.current_state = ControllerState()
        self.on_input_callback = on_input_callback
        self.input_thread = None
        
        # Button mapping for PS5 controller
        self.button_mapping = {
            0: 'X',
            1: 'O', 
            2: 'square',
            3: 'triangle',
            4: 'share',
            5: 'ps',
            6: 'options',
            7: 'left_stick',
            8: 'right_stick',
            9: 'L1',
            10: 'R1',
            11: 'dpad_up',
            12: 'dpad_down',
            13: 'dpad_left',
            14: 'dpad_right'
        }
        
        self.initialize_pygame()
    
    def initialize_pygame(self):
        """Initialize pygame for controller input"""
        try:
            pygame.init()
            pygame.joystick.init()
            logger.info("Pygame initialized for controller input")
        except Exception as e:
            logger.error(f"Error initializing pygame: {e}")
    
    def connect(self) -> bool:
        """Connect to PS5 controller"""
        try:
            if pygame.joystick.get_count() == 0:
                logger.warning("No controllers detected")
                return False
            
            self.controller = pygame.joystick.Joystick(0)
            self.controller.init()
            self.is_connected = True
            
            logger.info(f"Connected to controller: {self.controller.get_name()}")
            return True
            
        except Exception as e:
            logger.error(f"Error connecting to controller: {e}")
            return False
    
    def start_input_thread(self):
        """Start the input monitoring thread"""
        if not self.is_connected:
            logger.warning("Controller not connected")
            return
        
        self.is_running = True
        self.input_thread = threading.Thread(target=self._input_loop, daemon=True)
        self.input_thread.start()
        logger.info("Controller input thread started")
    
    def stop_input_thread(self):
        """Stop the input monitoring thread"""
        self.is_running = False
        if self.input_thread:
            self.input_thread.join(timeout=1.0)
        logger.info("Controller input thread stopped")
    
    def _input_loop(self):
        """Main input monitoring loop"""
        while self.is_running:
            try:
                pygame.event.pump()
                self._update_controller_state()
                
                if self.on_input_callback:
                    self.on_input_callback(self.current_state)
                
                time.sleep(1/60)  # 60 FPS polling rate
                
            except Exception as e:
                logger.error(f"Error in controller input loop: {e}")
                time.sleep(0.1)
    
    def _update_controller_state(self):
        """Update the current controller state"""
        if not self.controller:
            return
        
        # Update analog sticks
        self.current_state.left_stick_x = self.controller.get_axis(0)
        self.current_state.left_stick_y = self.controller.get_axis(1)
        self.current_state.right_stick_x = self.controller.get_axis(2)
        self.current_state.right_stick_y = self.controller.get_axis(3)
        
        # Update triggers
        self.current_state.left_trigger = self.controller.get_axis(4)
        self.current_state.right_trigger = self.controller.get_axis(5)
        
        # Update buttons
        for button_id, button_name in self.button_mapping.items():
            if button_id < self.controller.get_numbuttons():
                self.current_state.buttons[button_name] = self.controller.get_button(button_id)
        
        # Update D-pad (handled as hat)
        if self.controller.get_numhats() > 0:
            hat = self.controller.get_hat(0)
            self.current_state.buttons['dpad_left'] = hat[0] == -1
            self.current_state.buttons['dpad_right'] = hat[0] == 1
            self.current_state.buttons['dpad_up'] = hat[1] == 1
            self.current_state.buttons['dpad_down'] = hat[1] == -1
    
    def get_racing_input(self) -> Dict[str, float]:
        """Convert controller state to racing game inputs"""
        racing_input = {
            'steering': self.current_state.left_stick_x,  # -1 to 1
            'throttle': max(0, -self.current_state.left_stick_y),  # 0 to 1
            'brake': self.current_state.left_trigger,  # 0 to 1
            'boost': 1.0 if self.current_state.buttons['X'] else 0.0,
            'handbrake': 1.0 if self.current_state.buttons['square'] else 0.0
        }
        
        return racing_input
    
    def is_manual_override_active(self) -> bool:
        """Check if manual override is active"""
        # Consider manual override active if any significant input is detected
        racing_input = self.get_racing_input()
        
        threshold = 0.1
        return (abs(racing_input['steering']) > threshold or
                racing_input['throttle'] > threshold or
                racing_input['brake'] > threshold or
                racing_input['boost'] > 0 or
                racing_input['handbrake'] > 0)
    
    def get_menu_input(self) -> Dict[str, bool]:
        """Get input for menu navigation"""
        return {
            'up': self.current_state.buttons['dpad_up'] or self.current_state.left_stick_y < -0.5,
            'down': self.current_state.buttons['dpad_down'] or self.current_state.left_stick_y > 0.5,
            'left': self.current_state.buttons['dpad_left'] or self.current_state.left_stick_x < -0.5,
            'right': self.current_state.buttons['dpad_right'] or self.current_state.left_stick_x > 0.5,
            'select': self.current_state.buttons['X'],
            'back': self.current_state.buttons['O'],
            'menu': self.current_state.buttons['options'],
            'start': self.current_state.buttons['share']
        }
    
    def disconnect(self):
        """Disconnect the controller"""
        self.stop_input_thread()
        
        if self.controller:
            self.controller.quit()
            self.controller = None
        
        self.is_connected = False
        logger.info("Controller disconnected")

class ControllerManager:
    """Manages controller input and manual override functionality"""
    
    def __init__(self, game_interface):
        self.game_interface = game_interface
        self.ps5_controller = PS5Controller(on_input_callback=self._on_controller_input)
        self.manual_override_active = False
        self.last_manual_input_time = 0
        self.manual_timeout = 2.0  # Seconds of inactivity before returning to AI
        
    def initialize(self) -> bool:
        """Initialize controller manager"""
        try:
            if self.ps5_controller.connect():
                self.ps5_controller.start_input_thread()
                logger.info("Controller manager initialized successfully")
                return True
            else:
                logger.warning("Controller not available, manual override disabled")
                return False
        except Exception as e:
            logger.error(f"Error initializing controller manager: {e}")
            return False
    
    def _on_controller_input(self, controller_state: ControllerState):
        """Handle controller input callback"""
        try:
            # Check for manual override
            if self.ps5_controller.is_manual_override_active():
                self.manual_override_active = True
                self.last_manual_input_time = time.time()
                self._execute_manual_input()
            else:
                # Check if manual override should timeout
                if (self.manual_override_active and 
                    time.time() - self.last_manual_input_time > self.manual_timeout):
                    self.manual_override_active = False
                    logger.info("Manual override timed out, returning to AI control")
        
        except Exception as e:
            logger.error(f"Error handling controller input: {e}")
    
    def _execute_manual_input(self):
        """Execute manual controller input in the game"""
        try:
            racing_input = self.ps5_controller.get_racing_input()
            
            # Convert analog inputs to keyboard actions
            # This is a simplified mapping - you might want more sophisticated control
            
            # Steering
            if racing_input['steering'] < -0.3:
                self.game_interface.send_key_down(KEYBOARD_CONTROLS['left'])
            elif racing_input['steering'] > 0.3:
                self.game_interface.send_key_down(KEYBOARD_CONTROLS['right'])
            else:
                self.game_interface.send_key_up(KEYBOARD_CONTROLS['left'])
                self.game_interface.send_key_up(KEYBOARD_CONTROLS['right'])
            
            # Throttle/Brake
            if racing_input['throttle'] > 0.1:
                self.game_interface.send_key_down(KEYBOARD_CONTROLS['accelerate'])
            else:
                self.game_interface.send_key_up(KEYBOARD_CONTROLS['accelerate'])
            
            if racing_input['brake'] > 0.1:
                self.game_interface.send_key_down(KEYBOARD_CONTROLS['brake'])
            else:
                self.game_interface.send_key_up(KEYBOARD_CONTROLS['brake'])
            
            # Boost
            if racing_input['boost'] > 0:
                self.game_interface.send_key_down(KEYBOARD_CONTROLS['boost'])
            else:
                self.game_interface.send_key_up(KEYBOARD_CONTROLS['boost'])
            
            # Handbrake
            if racing_input['handbrake'] > 0:
                self.game_interface.send_key_down(KEYBOARD_CONTROLS['handbrake'])
            else:
                self.game_interface.send_key_up(KEYBOARD_CONTROLS['handbrake'])
                
        except Exception as e:
            logger.error(f"Error executing manual input: {e}")
    
    def is_manual_override_active(self) -> bool:
        """Check if manual override is currently active"""
        return self.manual_override_active
    
    def force_ai_control(self):
        """Force return to AI control"""
        self.manual_override_active = False
        logger.info("Forced return to AI control")
    
    def cleanup(self):
        """Cleanup controller resources"""
        if self.ps5_controller:
            self.ps5_controller.disconnect()
        logger.info("Controller manager cleaned up")
