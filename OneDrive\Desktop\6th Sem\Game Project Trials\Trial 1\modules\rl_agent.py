"""
Reinforcement Learning Agent for NFS: Most Wanted AI Agent
Implements Deep Q-Learning (DQN) for racing gameplay
"""
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from collections import deque
import random
import logging
from typing import Tuple, List, Optional
import pickle
from pathlib import Path

from config.settings import *

logger = logging.getLogger(__name__)

class DQNNetwork(nn.Module):
    """Deep Q-Network for racing game"""
    
    def __init__(self, input_shape: Tuple[int, int, int], action_size: int):
        super(DQNNetwork, self).__init__()
        
        self.input_shape = input_shape
        self.action_size = action_size
        
        # Convolutional layers for processing game frames
        self.conv1 = nn.Conv2d(input_shape[2], 32, kernel_size=8, stride=4)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=4, stride=2)
        self.conv3 = nn.Conv2d(64, 64, kernel_size=3, stride=1)
        
        # Calculate the size of flattened features
        conv_out_size = self._get_conv_out_size(input_shape)
        
        # Fully connected layers
        self.fc1 = nn.Linear(conv_out_size, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, action_size)
        
        # Dropout for regularization
        self.dropout = nn.Dropout(0.3)
        
    def _get_conv_out_size(self, shape):
        """Calculate the output size of convolutional layers"""
        with torch.no_grad():
            dummy_input = torch.zeros(1, shape[2], shape[0], shape[1])
            x = F.relu(self.conv1(dummy_input))
            x = F.relu(self.conv2(x))
            x = F.relu(self.conv3(x))
            return int(np.prod(x.size()))
    
    def forward(self, x):
        """Forward pass through the network"""
        # Ensure input is in correct format (batch, channels, height, width)
        if len(x.shape) == 3:
            x = x.unsqueeze(0)
        
        # Convolutional layers
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        x = F.relu(self.conv3(x))
        
        # Flatten
        x = x.view(x.size(0), -1)
        
        # Fully connected layers
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x

class ReplayBuffer:
    """Experience replay buffer for DQN"""
    
    def __init__(self, capacity: int):
        self.buffer = deque(maxlen=capacity)
    
    def push(self, state, action, reward, next_state, done):
        """Add experience to buffer"""
        self.buffer.append((state, action, reward, next_state, done))
    
    def sample(self, batch_size: int):
        """Sample batch of experiences"""
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done = map(np.stack, zip(*batch))
        return state, action, reward, next_state, done
    
    def __len__(self):
        return len(self.buffer)

class DQNAgent:
    """Deep Q-Learning Agent for racing game"""
    
    def __init__(self, state_shape: Tuple[int, int, int], action_size: int):
        self.state_shape = state_shape
        self.action_size = action_size
        self.device = torch.device(DEVICE)
        
        # Hyperparameters
        self.learning_rate = LEARNING_RATE
        self.batch_size = BATCH_SIZE
        self.gamma = 0.99  # Discount factor
        self.epsilon = EXPLORATION_RATE
        self.epsilon_decay = EXPLORATION_DECAY
        self.epsilon_min = EXPLORATION_MIN
        self.update_frequency = TRAINING_FREQUENCY
        self.target_update_frequency = TARGET_UPDATE_FREQUENCY
        
        # Networks
        self.q_network = DQNNetwork(state_shape, action_size).to(self.device)
        self.target_network = DQNNetwork(state_shape, action_size).to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=self.learning_rate)
        
        # Experience replay
        self.memory = ReplayBuffer(MEMORY_SIZE)
        
        # Training counters
        self.step_count = 0
        self.episode_count = 0
        
        # Performance tracking
        self.episode_rewards = []
        self.episode_lengths = []
        self.losses = []
        
        logger.info(f"DQN Agent initialized with state shape {state_shape} and {action_size} actions")
    
    def act(self, state: np.ndarray, training: bool = True) -> int:
        """Choose action using epsilon-greedy policy"""
        if training and random.random() < self.epsilon:
            return random.randrange(self.action_size)
        
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            q_values = self.q_network(state_tensor)
            return q_values.argmax().item()
    
    def remember(self, state, action, reward, next_state, done):
        """Store experience in replay buffer"""
        self.memory.push(state, action, reward, next_state, done)
    
    def replay(self):
        """Train the network on a batch of experiences"""
        if len(self.memory) < self.batch_size:
            return
        
        # Sample batch
        states, actions, rewards, next_states, dones = self.memory.sample(self.batch_size)
        
        # Convert to tensors
        states = torch.FloatTensor(states).to(self.device)
        actions = torch.LongTensor(actions).to(self.device)
        rewards = torch.FloatTensor(rewards).to(self.device)
        next_states = torch.FloatTensor(next_states).to(self.device)
        dones = torch.BoolTensor(dones).to(self.device)
        
        # Current Q values
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # Next Q values from target network
        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0]
            target_q_values = rewards + (self.gamma * next_q_values * ~dones)
        
        # Compute loss
        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)
        
        # Optimize
        self.optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), 1.0)
        self.optimizer.step()
        
        # Update epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
        
        # Update target network
        if self.step_count % self.target_update_frequency == 0:
            self.update_target_network()
        
        self.step_count += 1
        self.losses.append(loss.item())
        
        return loss.item()
    
    def update_target_network(self):
        """Copy weights from main network to target network"""
        self.target_network.load_state_dict(self.q_network.state_dict())
        logger.info("Target network updated")
    
    def calculate_reward(self, game_state: dict) -> float:
        """Calculate reward based on game state"""
        reward = 0.0
        
        # Speed reward
        if 'speed' in game_state:
            speed_normalized = min(game_state['speed'] / 200.0, 1.0)  # Normalize to 0-1
            reward += speed_normalized * REWARD_WEIGHTS['speed']
        
        # Position reward (if available)
        if 'position' in game_state:
            # Reward for being in front (position 1 is best)
            position_reward = (5 - game_state['position']) / 4.0  # Normalize for 5 positions
            reward += position_reward * REWARD_WEIGHTS['position']
        
        # Crash penalty
        if game_state.get('crashed', False):
            reward += REWARD_WEIGHTS['crash_penalty']
        
        # Wrong direction penalty
        if game_state.get('wrong_direction', False):
            reward += REWARD_WEIGHTS['wrong_direction_penalty']
        
        # Race completion bonus
        if game_state.get('race_finished', False):
            if game_state.get('position', 5) == 1:
                reward += REWARD_WEIGHTS['completion_bonus']
            else:
                reward += REWARD_WEIGHTS['completion_bonus'] * 0.5
        
        return reward
    
    def save_model(self, filepath: Optional[str] = None):
        """Save the trained model"""
        if filepath is None:
            filepath = RL_MODEL_PATH
        
        try:
            torch.save({
                'q_network_state_dict': self.q_network.state_dict(),
                'target_network_state_dict': self.target_network.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'epsilon': self.epsilon,
                'step_count': self.step_count,
                'episode_count': self.episode_count,
                'episode_rewards': self.episode_rewards,
                'episode_lengths': self.episode_lengths,
                'losses': self.losses
            }, filepath)
            logger.info(f"Model saved to {filepath}")
        except Exception as e:
            logger.error(f"Error saving model: {e}")
    
    def load_model(self, filepath: Optional[str] = None):
        """Load a trained model"""
        if filepath is None:
            filepath = RL_MODEL_PATH
        
        try:
            if Path(filepath).exists():
                checkpoint = torch.load(filepath, map_location=self.device)
                
                self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
                self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
                self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                self.epsilon = checkpoint.get('epsilon', self.epsilon)
                self.step_count = checkpoint.get('step_count', 0)
                self.episode_count = checkpoint.get('episode_count', 0)
                self.episode_rewards = checkpoint.get('episode_rewards', [])
                self.episode_lengths = checkpoint.get('episode_lengths', [])
                self.losses = checkpoint.get('losses', [])
                
                logger.info(f"Model loaded from {filepath}")
                return True
            else:
                logger.warning(f"Model file not found: {filepath}")
                return False
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False
    
    def get_training_stats(self) -> dict:
        """Get training statistics"""
        return {
            'episode_count': self.episode_count,
            'step_count': self.step_count,
            'epsilon': self.epsilon,
            'avg_reward': np.mean(self.episode_rewards[-100:]) if self.episode_rewards else 0,
            'avg_episode_length': np.mean(self.episode_lengths[-100:]) if self.episode_lengths else 0,
            'avg_loss': np.mean(self.losses[-100:]) if self.losses else 0
        }
    
    def reset_episode(self):
        """Reset for new episode"""
        self.episode_count += 1
