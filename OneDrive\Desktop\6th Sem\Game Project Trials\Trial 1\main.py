"""
Main AI Agent for NFS: Most Wanted (2012)
Integrates all modules to create a complete AI racing agent
"""
import time
import numpy as np
import cv2
import logging
import threading
from collections import deque
from typing import Optional, Dict, Any
import signal
import sys

# Import custom modules
from modules.game_interface import GameInterface
from modules.computer_vision import ComputerVision
from modules.rl_agent import DQNAgent
from modules.controller import Controller<PERSON>anager
from modules.voice_commands import VoiceCommandManager
from modules.logger import Game<PERSON>ogger
from config.settings import *

logger = logging.getLogger(__name__)

class NFSAIAgent:
    """Main AI Agent for Need for Speed: Most Wanted"""
    
    def __init__(self):
        # Core components
        self.game_interface = GameInterface()
        self.computer_vision = ComputerVision()
        self.rl_agent = DQNAgent(STATE_SHAPE, ACTION_SPACE_SIZE)
        self.controller_manager = ControllerManager(self.game_interface)
        self.voice_manager = VoiceCommandManager(self)
        self.logger = GameLogger()
        
        # State management
        self.is_running = False
        self.ai_mode_active = False
        self.current_episode = 0
        self.current_step = 0
        self.frame_buffer = deque(maxlen=STATE_SHAPE[2])
        self.last_action = 0
        self.episode_reward = 0.0
        self.episode_start_time = 0
        
        # Performance tracking
        self.fps_counter = 0
        self.fps_start_time = time.time()
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("NFS AI Agent initialized")
    
    def initialize(self) -> bool:
        """Initialize all components"""
        try:
            logger.info("Initializing AI Agent components...")
            
            # Initialize game interface
            if not self.game_interface.find_game_window():
                logger.error("Game window not found. Please start NFS: Most Wanted first.")
                return False
            
            # Load RL model if available
            self.rl_agent.load_model()
            
            # Initialize optional components
            controller_ok = self.controller_manager.initialize()
            voice_ok = self.voice_manager.initialize()
            
            if controller_ok:
                logger.info("PS5 Controller support enabled")
            else:
                logger.warning("PS5 Controller not available")
            
            if voice_ok:
                logger.info("Voice commands enabled")
            else:
                logger.warning("Voice commands not available")
            
            logger.info("AI Agent initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error during initialization: {e}")
            return False
    
    def start_ai_mode(self):
        """Start AI racing mode"""
        if not self.ai_mode_active:
            self.ai_mode_active = True
            self.current_episode += 1
            self.current_step = 0
            self.episode_reward = 0.0
            self.episode_start_time = time.time()
            self.frame_buffer.clear()
            
            self.logger.start_episode(self.current_episode)
            logger.info(f"AI mode started - Episode {self.current_episode}")
    
    def stop_ai_mode(self):
        """Stop AI racing mode"""
        if self.ai_mode_active:
            self.ai_mode_active = False
            self.logger.end_episode()
            
            # Log episode completion
            episode_duration = time.time() - self.episode_start_time
            self.logger.log_training(
                self.current_episode,
                self.current_step,
                self.episode_reward,
                self.current_step,
                self.rl_agent.epsilon,
                self.rl_agent.losses[-1] if self.rl_agent.losses else 0.0
            )
            
            logger.info(f"AI mode stopped - Episode {self.current_episode} completed")
    
    def pause_ai_mode(self):
        """Pause AI mode"""
        if self.ai_mode_active:
            self.ai_mode_active = False
            logger.info("AI mode paused")
    
    def resume_ai_mode(self):
        """Resume AI mode"""
        if not self.ai_mode_active:
            self.ai_mode_active = True
            logger.info("AI mode resumed")
    
    def take_screenshot(self):
        """Take and save screenshot"""
        filename = self.game_interface.save_screenshot()
        if filename:
            logger.info(f"Screenshot saved: {filename}")
        return filename
    
    def save_model(self):
        """Save the current RL model"""
        self.rl_agent.save_model()
        logger.info("Model saved")
    
    def get_current_state(self, frame: np.ndarray) -> np.ndarray:
        """Process frame and return state for RL agent"""
        try:
            # Preprocess frame
            processed_frame = self.game_interface.preprocess_frame(frame)
            
            # Add to frame buffer
            self.frame_buffer.append(processed_frame)
            
            # If buffer not full, pad with zeros
            while len(self.frame_buffer) < STATE_SHAPE[2]:
                self.frame_buffer.append(np.zeros(STATE_SHAPE[:2], dtype=np.float32))
            
            # Stack frames
            state = np.stack(list(self.frame_buffer), axis=2)
            
            return state
            
        except Exception as e:
            logger.error(f"Error getting current state: {e}")
            return np.zeros(STATE_SHAPE, dtype=np.float32)
    
    def analyze_game_state(self, frame: np.ndarray) -> Dict[str, Any]:
        """Analyze current game state using computer vision"""
        try:
            game_state = {
                'state': self.computer_vision.detect_game_state(frame),
                'speed': 0,
                'position': 1,
                'crashed': False,
                'wrong_direction': False,
                'race_finished': False
            }
            
            # Extract additional information
            speed_info = self.computer_vision.extract_speed_info(frame)
            if speed_info:
                game_state['speed'] = speed_info
            
            # Detect crashes or special states
            if game_state['state'] == 'crash_screen':
                game_state['crashed'] = True
            elif game_state['state'] == 'race_finished':
                game_state['race_finished'] = True
            
            return game_state
            
        except Exception as e:
            logger.error(f"Error analyzing game state: {e}")
            return {'state': 'unknown', 'speed': 0, 'position': 1, 'crashed': False}
    
    def main_loop(self):
        """Main AI agent loop"""
        logger.info("Starting main AI agent loop")
        self.is_running = True
        
        try:
            while self.is_running:
                loop_start_time = time.time()
                
                # Check if game is still running
                if not self.game_interface.is_game_running():
                    logger.warning("Game not running, waiting...")
                    time.sleep(5)
                    continue
                
                # Focus game window
                self.game_interface.focus_game_window()
                
                # Capture screen
                frame = self.game_interface.capture_screen()
                if frame is None:
                    time.sleep(0.1)
                    continue
                
                # Analyze game state
                game_state = self.analyze_game_state(frame)
                
                # Handle different game states
                if game_state['state'] == 'loading_screen':
                    time.sleep(1)
                    continue
                elif game_state['state'] == 'car_selection':
                    self.handle_car_selection(frame)
                    continue
                elif game_state['state'] == 'map_screen':
                    self.handle_map_screen(frame)
                    continue
                elif game_state['state'] == 'pause_menu':
                    self.handle_pause_menu(frame)
                    continue
                
                # Check for manual override
                if self.controller_manager.is_manual_override_active():
                    time.sleep(0.1)
                    continue
                
                # AI racing logic
                if self.ai_mode_active and game_state['state'] == 'in_race':
                    self.handle_racing(frame, game_state)
                
                # Process voice commands
                voice_commands = self.voice_manager.voice_commands.get_pending_commands()
                for command in voice_commands:
                    logger.info(f"Processing voice command: {command}")
                
                # FPS tracking
                self.fps_counter += 1
                if time.time() - self.fps_start_time >= 1.0:
                    fps = self.fps_counter / (time.time() - self.fps_start_time)
                    if DEBUG_MODE:
                        logger.debug(f"FPS: {fps:.1f}")
                    self.fps_counter = 0
                    self.fps_start_time = time.time()
                
                # Maintain target FPS
                loop_duration = time.time() - loop_start_time
                target_duration = 1.0 / TARGET_FPS
                if loop_duration < target_duration:
                    time.sleep(target_duration - loop_duration)
                
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
        finally:
            self.cleanup()
    
    def handle_racing(self, frame: np.ndarray, game_state: Dict[str, Any]):
        """Handle AI racing logic"""
        try:
            # Get current state for RL agent
            state = self.get_current_state(frame)

            # Transpose state to match PyTorch format (channels, height, width)
            if len(state.shape) == 3:
                state = np.transpose(state, (2, 0, 1))

            # Choose action
            action = self.rl_agent.act(state, training=True)
            
            # Execute action
            self.game_interface.execute_action(action)
            
            # Calculate reward
            reward = self.rl_agent.calculate_reward(game_state)
            self.episode_reward += reward
            
            # Store experience for training
            if hasattr(self, 'previous_state') and hasattr(self, 'previous_action'):
                self.rl_agent.remember(
                    self.previous_state,
                    self.previous_action,
                    reward,
                    state,
                    game_state.get('race_finished', False) or game_state.get('crashed', False)
                )
                
                # Train the agent
                if self.current_step % TRAINING_FREQUENCY == 0:
                    loss = self.rl_agent.replay()
            
            # Log performance
            self.logger.log_performance(
                self.current_episode,
                self.current_step,
                action,
                reward,
                game_state,
                0.0  # AI confidence placeholder
            )
            
            # Update state
            self.previous_state = state
            self.previous_action = action
            self.last_action = action
            self.current_step += 1
            
            # Check for episode end conditions
            if (game_state.get('race_finished', False) or 
                game_state.get('crashed', False) or
                time.time() - self.episode_start_time > MAX_EPISODE_LENGTH):
                self.stop_ai_mode()
                time.sleep(2)  # Brief pause before next episode
                self.start_ai_mode()
            
        except Exception as e:
            logger.error(f"Error in racing handler: {e}")
    
    def handle_car_selection(self, frame: np.ndarray):
        """Handle car selection screen"""
        try:
            cars = self.computer_vision.detect_cars_in_selection(frame)
            if cars:
                # Select first available car
                car = cars[0]
                self.game_interface.click_at_position(car['center'][0], car['center'][1])
                time.sleep(1)
                
                # Look for start button
                start_button = self.computer_vision.template_match(frame, 'start_race')
                if start_button:
                    self.game_interface.click_at_position(start_button[0], start_button[1])
                    
        except Exception as e:
            logger.error(f"Error handling car selection: {e}")
    
    def handle_map_screen(self, frame: np.ndarray):
        """Handle map/race selection screen"""
        try:
            # Look for clickable race elements
            clickable_elements = self.computer_vision.find_clickable_elements(frame)
            if clickable_elements:
                # Click on first race option
                element = clickable_elements[0]
                self.game_interface.click_at_position(element['position'][0], element['position'][1])
                time.sleep(1)
                
        except Exception as e:
            logger.error(f"Error handling map screen: {e}")
    
    def handle_pause_menu(self, frame: np.ndarray):
        """Handle pause menu"""
        try:
            # Press escape to resume
            self.game_interface.send_key_press('esc')
            time.sleep(0.5)
            
        except Exception as e:
            logger.error(f"Error handling pause menu: {e}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.is_running = False
    
    def cleanup(self):
        """Cleanup all resources"""
        try:
            logger.info("Cleaning up AI Agent...")
            
            self.is_running = False
            
            if self.ai_mode_active:
                self.stop_ai_mode()
            
            # Save final model
            self.rl_agent.save_model()
            
            # Cleanup components
            self.game_interface.cleanup()
            self.controller_manager.cleanup()
            self.voice_manager.cleanup()
            self.logger.cleanup()
            
            logger.info("AI Agent cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

def main():
    """Main entry point"""
    try:
        logger.info("Starting NFS: Most Wanted AI Agent")
        
        # Create and initialize agent
        agent = NFSAIAgent()
        
        if not agent.initialize():
            logger.error("Failed to initialize AI Agent")
            return 1
        
        # Enable optional features
        if input("Enable voice commands? (y/n): ").lower() == 'y':
            agent.voice_manager.enable_voice_control()
        
        # Start AI mode
        agent.start_ai_mode()
        
        # Run main loop
        agent.main_loop()
        
        return 0
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
