# Core dependencies
torch>=2.0.0
torchvision>=0.15.0
opencv-python>=4.8.0
numpy>=1.24.0
pillow>=10.0.0
gymnasium>=0.29.0
stable-baselines3>=2.0.0
ultralytics>=8.0.0

# Screen capture and input
mss>=9.0.0
pyautogui>=0.9.54
pynput>=1.7.6
pygetwindow>=0.0.9

# Audio and speech
speechrecognition>=3.10.0
pyttsx3>=2.90
pyaudio>=0.2.11

# Controller support
pygame>=2.5.0
inputs>=0.5

# Data processing and visualization
pandas>=2.0.0
matplotlib>=3.7.0
seaborn>=0.12.0
scikit-learn>=1.3.0

# Utilities
tqdm>=4.65.0
psutil>=5.9.0
configparser>=5.3.0
python-dotenv>=1.0.0

# Optional GPU acceleration
# cupy-cuda11x>=12.0.0  # Uncomment if using CUDA 11.x
# cupy-cuda12x>=12.0.0  # Uncomment if using CUDA 12.x
